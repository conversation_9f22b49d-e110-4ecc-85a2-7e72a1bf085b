#!/bin/bash

# RocketMQ Docker 停止脚本
# 用于停止本地RocketMQ开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 停止服务
stop_services() {
    print_message $BLUE "停止RocketMQ服务..."
    
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
        print_message $GREEN "服务已停止"
    else
        print_message $RED "错误: 找不到docker-compose.yml文件"
        exit 1
    fi
}

# 清理数据（可选）
clean_data() {
    if [ "$1" = "--clean" ] || [ "$1" = "-c" ]; then
        print_message $YELLOW "警告: 即将删除所有数据，包括消息、日志等"
        read -p "确认删除所有数据? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_message $BLUE "清理数据目录..."
            sudo rm -rf data/
            print_message $GREEN "数据清理完成"
        else
            print_message $YELLOW "取消数据清理"
        fi
    fi
}

# 显示状态
show_status() {
    print_message $BLUE "检查容器状态..."
    
    # 检查是否还有相关容器在运行
    running_containers=$(docker ps --filter "name=rocketmq" --filter "name=user-center" --format "table {{.Names}}\t{{.Status}}" | tail -n +2)
    
    if [ -z "$running_containers" ]; then
        print_message $GREEN "所有相关容器已停止"
    else
        print_message $YELLOW "以下容器仍在运行:"
        echo "$running_containers"
    fi
}

# 显示帮助信息
show_help() {
    echo "RocketMQ Docker 停止脚本"
    echo ""
    echo "用法:"
    echo "  ./stop.sh              # 停止服务"
    echo "  ./stop.sh --clean      # 停止服务并清理数据"
    echo "  ./stop.sh -c           # 停止服务并清理数据"
    echo "  ./stop.sh --help       # 显示帮助信息"
    echo ""
    echo "选项:"
    echo "  --clean, -c    停止服务后清理所有数据（谨慎使用）"
    echo "  --help, -h     显示此帮助信息"
}

# 主函数
main() {
    # 检查参数
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        show_help
        exit 0
    fi
    
    print_message $GREEN "=== RocketMQ Docker 环境停止脚本 ==="
    echo ""
    
    # 检查是否在正确的目录
    if [ ! -f "docker-compose.yml" ]; then
        print_message $RED "错误: 请在包含docker-compose.yml的目录中运行此脚本"
        exit 1
    fi
    
    stop_services
    clean_data "$1"
    show_status
    
    print_message $GREEN "RocketMQ环境停止完成！"
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
