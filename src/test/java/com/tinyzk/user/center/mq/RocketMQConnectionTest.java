package com.tinyzk.user.center.mq;

import com.tinyzk.user.center.dto.ResumeParseMessage;
import com.tinyzk.user.center.service.MessageProducerService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RocketMQ连接和功能测试
 */
@SpringBootTest
@ActiveProfiles("local")
@Slf4j
class RocketMQConnectionTest {

    @Autowired
    private MessageProducerService messageProducer;

    /**
     * 测试RocketMQ连接
     */
    @Test
    void testRocketMQConnection() {
        log.info("开始测试RocketMQ连接...");
        
        // 创建测试消息
        ResumeParseMessage testMessage = createTestMessage();
        
        // 发送消息不应该抛出异常
        assertDoesNotThrow(() -> {
            messageProducer.sendResumeParseMessage(testMessage);
            log.info("RocketMQ连接测试成功，消息发送正常: messageId={}", testMessage.getMessageId());
        }, "RocketMQ连接失败或消息发送异常");
    }

    /**
     * 测试简历解析消息发送
     */
    @Test
    void testSendResumeParseMessage() {
        log.info("开始测试简历解析消息发送...");
        
        ResumeParseMessage message = createTestMessage();
        message.setFileName("test_resume_parse.pdf");
        message.setFileType("pdf");
        
        assertDoesNotThrow(() -> {
            messageProducer.sendResumeParseMessage(message);
            log.info("简历解析消息发送成功: messageId={}", message.getMessageId());
        });
    }

    /**
     * 测试文件上传消息发送
     */
    @Test
    void testSendFileUploadMessage() {
        log.info("开始测试文件上传消息发送...");
        
        String testMessage = String.format(
            "{\"messageId\":\"%s\",\"fileName\":\"test_upload.pdf\",\"uploadTime\":\"%d\"}",
            UUID.randomUUID().toString(),
            System.currentTimeMillis()
        );
        
        assertDoesNotThrow(() -> {
            messageProducer.sendFileUploadMessage(testMessage);
            log.info("文件上传消息发送成功");
        });
    }

    /**
     * 测试通用消息发送
     */
    @Test
    void testSendGeneralMessage() {
        log.info("开始测试通用消息发送...");
        
        String topic = "TEST_TOPIC";
        String tag = "TEST";
        String message = String.format(
            "{\"messageId\":\"%s\",\"content\":\"Test message\",\"timestamp\":\"%d\"}",
            UUID.randomUUID().toString(),
            System.currentTimeMillis()
        );
        
        assertDoesNotThrow(() -> {
            messageProducer.sendGeneralMessage(topic, tag, message);
            log.info("通用消息发送成功: topic={}, tag={}", topic, tag);
        });
    }

    /**
     * 测试批量消息发送
     */
    @Test
    void testBatchMessageSending() {
        log.info("开始测试批量消息发送...");
        
        int messageCount = 10;
        String batchId = "test_batch_" + System.currentTimeMillis();
        
        for (int i = 1; i <= messageCount; i++) {
            ResumeParseMessage message = createTestMessage();
            message.setBatchId(batchId);
            message.setFileName("batch_test_" + i + ".pdf");
            
            final int messageIndex = i;
            assertDoesNotThrow(() -> {
                messageProducer.sendResumeParseMessage(message);
                log.debug("批量消息 {}/{} 发送成功: messageId={}", messageIndex, messageCount, message.getMessageId());
            });
            
            // 添加小延迟避免发送过快
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        log.info("批量消息发送测试完成: batchId={}, count={}", batchId, messageCount);
    }

    /**
     * 测试消息发送性能
     */
    @Test
    void testMessageSendingPerformance() {
        log.info("开始测试消息发送性能...");
        
        int messageCount = 100;
        long startTime = System.currentTimeMillis();
        
        for (int i = 1; i <= messageCount; i++) {
            ResumeParseMessage message = createTestMessage();
            message.setFileName("performance_test_" + i + ".pdf");
            
            assertDoesNotThrow(() -> {
                messageProducer.sendResumeParseMessage(message);
            });
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double messagesPerSecond = (double) messageCount / (duration / 1000.0);
        
        log.info("性能测试完成: 发送{}条消息耗时{}ms, 平均{:.2f}条/秒", 
                messageCount, duration, messagesPerSecond);
        
        // 验证性能指标（根据实际需求调整）
        assertTrue(messagesPerSecond > 10, "消息发送性能过低: " + messagesPerSecond + " 条/秒");
    }

    /**
     * 测试并发消息发送
     */
    @Test
    void testConcurrentMessageSending() throws InterruptedException {
        log.info("开始测试并发消息发送...");
        
        int threadCount = 5;
        int messagesPerThread = 20;
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int t = 1; t <= threadCount; t++) {
            final int threadIndex = t;
            new Thread(() -> {
                try {
                    for (int i = 1; i <= messagesPerThread; i++) {
                        ResumeParseMessage message = createTestMessage();
                        message.setFileName("concurrent_test_t" + threadIndex + "_m" + i + ".pdf");
                        
                        messageProducer.sendResumeParseMessage(message);
                        Thread.sleep(10); // 小延迟
                    }
                    log.info("线程 {} 完成发送 {} 条消息", threadIndex, messagesPerThread);
                } catch (Exception e) {
                    log.error("线程 {} 发送消息失败", threadIndex, e);
                } finally {
                    latch.countDown();
                }
            }).start();
        }
        
        // 等待所有线程完成，最多等待30秒
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        assertTrue(completed, "并发消息发送测试超时");
        
        log.info("并发消息发送测试完成: {} 个线程，每线程 {} 条消息", threadCount, messagesPerThread);
    }

    /**
     * 创建测试消息
     */
    private ResumeParseMessage createTestMessage() {
        ResumeParseMessage message = new ResumeParseMessage();
        message.setMessageId(UUID.randomUUID().toString());
        message.setBatchId("test_batch_" + System.currentTimeMillis());
        message.setFileName("test_resume.pdf");
        message.setFileUrl("test/path/test_resume.pdf");
        message.setUserId(1L);
        message.setFileSize(1024L * 100); // 100KB
        message.setFileType("pdf");
        message.setFileHash("test_hash_" + System.currentTimeMillis());
        return message;
    }
}
