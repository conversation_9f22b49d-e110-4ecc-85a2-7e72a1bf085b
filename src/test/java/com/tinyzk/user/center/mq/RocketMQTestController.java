package com.tinyzk.user.center.mq;

import com.tinyzk.user.center.common.Result;
import com.tinyzk.user.center.dto.ResumeParseMessage;
import com.tinyzk.user.center.service.MessageProducerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * RocketMQ测试控制器
 * 用于测试消息队列的发送和接收功能
 */
@RestController
@RequestMapping("/test/mq")
@Tag(name = "RocketMQ测试", description = "RocketMQ消息队列测试接口")
@Slf4j
public class RocketMQTestController {

    @Autowired
    private MessageProducerService messageProducer;

    /**
     * 测试发送简历解析消息
     */
    @PostMapping("/send-resume-parse")
    @Operation(summary = "发送简历解析测试消息", description = "发送一条测试的简历解析消息到RocketMQ")
    public Result<String> sendResumeParseMessage(
            @RequestParam(value = "fileName", defaultValue = "test_resume.pdf") String fileName,
            @RequestParam(value = "userId", defaultValue = "1") Long userId) {
        
        try {
            // 创建测试消息
            ResumeParseMessage message = new ResumeParseMessage();
            message.setMessageId(UUID.randomUUID().toString());
            message.setBatchId("test_batch_" + System.currentTimeMillis());
            message.setFileName(fileName);
            message.setFileUrl("test/path/" + fileName);
            message.setUserId(userId);
            message.setFileSize(1024L * 100); // 100KB
            message.setFileType(getFileType(fileName));
            message.setFileHash("test_hash_" + System.currentTimeMillis());

            log.info("准备发送测试消息: messageId={}, fileName={}, userId={}", 
                    message.getMessageId(), fileName, userId);

            // 发送消息
            messageProducer.sendResumeParseMessage(message);

            log.info("测试消息发送成功: messageId={}", message.getMessageId());
            return Result.success(message.getMessageId(), "消息发送成功");

        } catch (Exception e) {
            log.error("测试消息发送失败: fileName={}, userId={}", fileName, userId, e);
            return Result.error("消息发送失败: " + e.getMessage());
        }
    }

    /**
     * 批量发送测试消息
     */
    @PostMapping("/send-batch-messages")
    @Operation(summary = "批量发送测试消息", description = "批量发送多条简历解析测试消息")
    public Result<String> sendBatchMessages(
            @RequestParam(value = "count", defaultValue = "5") int count,
            @RequestParam(value = "userId", defaultValue = "1") Long userId) {
        
        try {
            String batchId = "test_batch_" + System.currentTimeMillis();
            int successCount = 0;
            int failureCount = 0;

            log.info("开始批量发送测试消息: count={}, batchId={}", count, batchId);

            for (int i = 1; i <= count; i++) {
                try {
                    ResumeParseMessage message = new ResumeParseMessage();
                    message.setMessageId(UUID.randomUUID().toString());
                    message.setBatchId(batchId);
                    message.setFileName("test_resume_" + i + ".pdf");
                    message.setFileUrl("test/path/test_resume_" + i + ".pdf");
                    message.setUserId(userId);
                    message.setFileSize(1024L * (100 + i * 10)); // 递增文件大小
                    message.setFileType("pdf");
                    message.setFileHash("test_hash_" + i + "_" + System.currentTimeMillis());

                    messageProducer.sendResumeParseMessage(message);
                    successCount++;
                    
                    // 添加小延迟避免发送过快
                    Thread.sleep(100);
                    
                } catch (Exception e) {
                    log.error("发送第{}条消息失败", i, e);
                    failureCount++;
                }
            }

            String result = String.format("批量发送完成 - 成功: %d, 失败: %d, 批次ID: %s", 
                    successCount, failureCount, batchId);
            
            log.info(result);
            return Result.success(batchId, result);

        } catch (Exception e) {
            log.error("批量发送测试消息失败: count={}, userId={}", count, userId, e);
            return Result.error("批量发送失败: " + e.getMessage());
        }
    }

    /**
     * 测试发送文件上传消息
     */
    @PostMapping("/send-file-upload")
    @Operation(summary = "发送文件上传测试消息", description = "发送一条测试的文件上传消息到RocketMQ")
    public Result<String> sendFileUploadMessage(
            @RequestParam(value = "fileName", defaultValue = "test_file.pdf") String fileName) {
        
        try {
            // 创建文件上传消息（简化版）
            String messageId = UUID.randomUUID().toString();
            String message = String.format("{\"messageId\":\"%s\",\"fileName\":\"%s\",\"uploadTime\":\"%d\"}", 
                    messageId, fileName, System.currentTimeMillis());

            log.info("准备发送文件上传测试消息: messageId={}, fileName={}", messageId, fileName);

            // 发送消息
            messageProducer.sendFileUploadMessage(message);

            log.info("文件上传测试消息发送成功: messageId={}", messageId);
            return Result.success(messageId, "文件上传消息发送成功");

        } catch (Exception e) {
            log.error("文件上传测试消息发送失败: fileName={}", fileName, e);
            return Result.error("文件上传消息发送失败: " + e.getMessage());
        }
    }

    /**
     * 测试发送通用消息
     */
    @PostMapping("/send-general-message")
    @Operation(summary = "发送通用测试消息", description = "发送一条通用测试消息到指定Topic")
    public Result<String> sendGeneralMessage(
            @RequestParam(value = "topic", defaultValue = "TEST_TOPIC") String topic,
            @RequestParam(value = "tag", defaultValue = "TEST") String tag,
            @RequestParam(value = "content", defaultValue = "Hello RocketMQ!") String content) {
        
        try {
            String messageId = UUID.randomUUID().toString();
            String message = String.format("{\"messageId\":\"%s\",\"content\":\"%s\",\"timestamp\":\"%d\"}", 
                    messageId, content, System.currentTimeMillis());

            log.info("准备发送通用测试消息: topic={}, tag={}, messageId={}", topic, tag, messageId);

            // 发送消息
            messageProducer.sendGeneralMessage(topic, tag, message);

            log.info("通用测试消息发送成功: messageId={}", messageId);
            return Result.success(messageId, "通用消息发送成功");

        } catch (Exception e) {
            log.error("通用测试消息发送失败: topic={}, tag={}, content={}", topic, tag, content, e);
            return Result.error("通用消息发送失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件类型
     */
    private String getFileType(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "unknown";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }
}
