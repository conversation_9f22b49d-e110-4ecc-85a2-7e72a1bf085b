## 服务配置
spring:
  application:
    name: user-center
  profiles:
    active: local
  aop:
    proxy-target-class: true
    
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      config:
        import-check:
          enabled: false
      discovery:
        enabled: false

management:
  server:
    port: 18080
  health:
    elasticsearch:
      enabled: false
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true
  metrics:
    distribution:
      percentiles-histogram:
        "[http.server.requests]": true
      percentiles:
        "[http.server.requests]": 0.5, 0.95, 0.99
    tags:
      application: ${spring.application.name}
  tracing:
    sampling:
      probability: 0.1
  # SkyWalking 配置通过 Agent 参数设置，无需在配置文件中配置

# RocketMQ配置
rocketmq:
  name-server: http://MQ_INST_1708662203807002_BXfU8rhL.cn-shanghai.mq-internal.aliyuncs.com:8080
  producer:
    group: user-center-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    max-message-size: 4194304  # 4MB
    compress-message-body-threshold: 4096
    access-key: LTAI5tL6PCY7TQgJTRw2GuV8
    secret-key: ******************************
  consumer:
    group: user-center-consumer-group
    consume-thread-min: 5
    consume-thread-max: 20
    consume-message-batch-max-size: 1
    access-key: LTAI5tL6PCY7TQgJTRw2GuV8
    secret-key: ******************************

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: https://oss-cn-shanghai.aliyuncs.com
    access-key-id: LTAI5tFmSq5AVvZ7XigbM3Vt
    access-key-secret: ******************************
    bucket-name: tzk-resume
    connection-timeout: 3000
    socket-timeout: 30000
    max-connections: 50
    max-error-retry: 3
    use-internal-endpoint: false

# 线程池配置
async:
  task:
    executor:
      core-pool-size: 5
      max-pool-size: 15
      queue-capacity: 200
      keep-alive-seconds: 60
      thread-name-prefix: "AsyncTask-"
      batch-resume-parse-executor:
        core-pool-size: 3
        max-pool-size: 8
        queue-capacity: 100
        keep-alive-seconds: 300
        thread-name-prefix: "BatchResumeParser-"
      api-call-executor:
        core-pool-size: 2
        max-pool-size: 5
        queue-capacity: 50
        keep-alive-seconds: 60
        thread-name-prefix: "ApiCall-"
      cache-warmup-executor:
        core-pool-size: 3
        max-pool-size: 8
        queue-capacity: 200
        keep-alive-seconds: 60
        thread-name-prefix: "CacheWarmup-"
      oss-upload-executor:
        core-pool-size: 8
        max-pool-size: 16
        queue-capacity: 200
        keep-alive-seconds: 300
        thread-name-prefix: "OSS-Upload-"

# Resilience4j配置
resilience4j:
  # 熔断器配置
  circuitbreaker:
    instances:
      third-party-api:
        failure-rate-threshold: 60
        wait-duration-in-open-state: 60s
        sliding-window-size: 10
        minimum-number-of-calls: 5
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 10s

  # 限流器配置
  ratelimiter:
    instances:
      message-producer:
        limit-for-period: 50
        limit-refresh-period: 1s
        timeout-duration: 100ms
      message-consumer:
        limit-for-period: 30
        limit-refresh-period: 1s
        timeout-duration: 100ms

  # 重试配置
  retry:
    instances:
      third-party-api:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
        retry-exceptions:
          - java.net.SocketTimeoutException
          - java.net.ConnectException
          - org.springframework.web.client.HttpServerErrorException

# 缓存预热配置
cache:
  warmup:
    enabled: true
    startup-warmup: true
    schedule:
      enabled: true
      cron: "0 0 2 * * ?"
      before-expire: PT2M
    strategy:
      user-detail-count: 1000
      user-list-pages: 5
      page-size: 20
      hot-data-days: 7
      priorities:
        - userDetail
        - userList
        - clientDetails
        - userMapping
    resource:
      thread-pool-size: 5
      timeout: PT30M
      batch-size: 100
      interval: PT0.1S